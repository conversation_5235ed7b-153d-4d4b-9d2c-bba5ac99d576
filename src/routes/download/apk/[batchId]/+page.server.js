import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';

/**
 * Load function for the public APK download page
 * This page allows downloading APKs without authentication using a batch ID
 */
export async function load({ params }) {
  const { batchId } = params;

  // Validate batch ID format (should start with "batch-" followed by timestamp)
  if (!batchId || !batchId.startsWith('batch-')) {
    throw error(404, 'Invalid batch ID');
  }

  try {
    // Extract timestamp from batch ID
    const timestampMatch = batchId.match(/^batch-(\d+)$/);
    if (!timestampMatch) {
      throw error(404, 'Invalid batch ID format');
    }

    const timestamp = timestampMatch[1];
    
    // Look for directories that contain this batch ID
    const tmpDir = ApkRepackerService.TMP_DIR;
    
    if (!fs.existsSync(tmpDir)) {
      throw error(404, 'No APK files found');
    }

    const dirs = fs.readdirSync(tmpDir);
    
    // Find directories that match this batch
    const batchDirs = dirs.filter(dir => dir.includes(batchId));
    
    if (batchDirs.length === 0) {
      throw error(404, 'No APK files found for this batch');
    }

    // Collect all APK files from batch directories
    const apkFiles = [];
    
    for (const dir of batchDirs) {
      const dirPath = path.join(tmpDir, dir);
      
      try {
        const files = fs.readdirSync(dirPath);
        // Only include final repacked APKs, exclude input.apk and temp files
        const apkFilesInDir = files.filter(file =>
          file.toLowerCase().endsWith('.apk') &&
          !file.toLowerCase().includes('input') &&
          !file.toLowerCase().includes('temp') &&
          file.startsWith('template-') // Only include files that start with 'template-'
        );

        for (const apkFile of apkFilesInDir) {
          const filePath = path.join(dirPath, apkFile);
          const stats = fs.statSync(filePath);

          // Extract device info from directory name if possible
          const deviceMatch = dir.match(/device-([^-]+)/);
          const deviceId = deviceMatch ? deviceMatch[1] : 'unknown';

          // Extract template info from filename
          const templateMatch = apkFile.match(/^template-(\d+)-(\d+)\.apk$/);
          const templateId = templateMatch ? templateMatch[1] : 'unknown';

          apkFiles.push({
            filename: apkFile,
            deviceId,
            templateId,
            size: stats.size,
            created: stats.birthtime,
            downloadUrl: `/download/apk/${batchId}/${dir}/${apkFile}`
          });
        }
      } catch (err) {
        console.error(`Error reading directory ${dir}:`, err);
      }
    }

    if (apkFiles.length === 0) {
      throw error(404, 'No APK files found in batch');
    }

    // Sort by creation time (newest first)
    apkFiles.sort((a, b) => b.created.getTime() - a.created.getTime());

    return {
      batchId,
      apkFiles,
      totalFiles: apkFiles.length,
      batchCreated: new Date(parseInt(timestamp))
    };
  } catch (err) {
    console.error('Error loading batch APK files:', err);
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    throw error(500, 'Error loading APK files');
  }
}
