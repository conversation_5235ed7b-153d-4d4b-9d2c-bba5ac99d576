import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';

/**
 * Load function for the public APK download page
 * This page allows downloading APKs without authentication using a batch ID
 */
export async function load({ params }) {
  const { batchId } = params;

  // Validate batch ID format (should start with "batch-" followed by timestamp)
  if (!batchId || !batchId.startsWith('batch-')) {
    throw error(404, 'Invalid batch ID');
  }

  try {
    // Extract timestamp from batch ID
    const timestampMatch = batchId.match(/^batch-(\d+)$/);
    if (!timestampMatch) {
      throw error(404, 'Invalid batch ID format');
    }

    const timestamp = timestampMatch[1];
    
    // Look for directories that contain this batch ID
    const tmpDir = ApkRepackerService.TMP_DIR;
    
    if (!fs.existsSync(tmpDir)) {
      throw error(404, 'No APK files found');
    }

    const dirs = fs.readdirSync(tmpDir);
    
    // Find directories that match this batch
    const batchDirs = dirs.filter(dir => dir.includes(batchId));

    console.log(`[APK Download] Looking for batch: ${batchId}`);
    console.log(`[APK Download] Found directories: ${dirs.join(', ')}`);
    console.log(`[APK Download] Matching batch directories: ${batchDirs.join(', ')}`);

    if (batchDirs.length === 0) {
      console.log(`[APK Download] No directories found for batch ${batchId}`);
      console.log(`[APK Download] Available directories: ${dirs.join(', ')}`);
      throw error(404, `No APK files found for batch ${batchId}. Available directories: ${dirs.slice(0, 5).join(', ')}${dirs.length > 5 ? '...' : ''}`);
    }

    // Collect all APK files from batch directories
    const apkFiles = [];
    
    for (const dir of batchDirs) {
      const dirPath = path.join(tmpDir, dir);
      
      try {
        const files = fs.readdirSync(dirPath);
        console.log(`[APK Download] Files in ${dir}: ${files.join(', ')}`);

        // Only include final repacked APKs, exclude input.apk and temp files
        console.log(`[APK Download] Testing filter for each file in ${dir}:`);
        files.forEach(file => {
          const isApk = file.toLowerCase().endsWith('.apk');
          const hasInput = file.toLowerCase().includes('input');
          const hasTemp = file.toLowerCase().includes('temp');
          const passes = isApk && !hasInput && !hasTemp;
          console.log(`  ${file}: isApk=${isApk}, hasInput=${hasInput}, hasTemp=${hasTemp}, passes=${passes}`);
        });

        const apkFilesInDir = files.filter(file =>
          file.toLowerCase().endsWith('.apk') &&
          !file.toLowerCase().includes('input') &&
          !file.toLowerCase().includes('temp')
        );

        console.log(`[APK Download] Filtered APK files in ${dir}: ${apkFilesInDir.join(', ')}`);

        console.log(`[APK Download] Processing ${apkFilesInDir.length} APK files in ${dir}`);

        for (const apkFile of apkFilesInDir) {
          try {
            const filePath = path.join(dirPath, apkFile);
            const stats = fs.statSync(filePath);

            console.log(`[APK Download] Processing file: ${apkFile}, size: ${stats.size}`);

            // Extract device info from directory name if possible
            const deviceMatch = dir.match(/device-([^-]+)/);
            const deviceId = deviceMatch ? deviceMatch[1] : 'unknown';

            // Extract template info from filename - be more flexible with the pattern
            let templateId = 'unknown';

            // Try different patterns for template ID extraction
            const templateMatch1 = apkFile.match(/^template-(\d+)-(\d+)\.apk$/);
            const templateMatch2 = apkFile.match(/^(.+)-(\d{4,})\.apk$/);
            const templateMatch3 = apkFile.match(/^(.+)\.apk$/);

            if (templateMatch1) {
              templateId = templateMatch1[1];
            } else if (templateMatch2) {
              templateId = templateMatch2[1];
            } else if (templateMatch3) {
              templateId = templateMatch3[1];
            }

            const apkFileInfo = {
              filename: apkFile,
              deviceId,
              templateId,
              size: stats.size,
              created: stats.birthtime,
              downloadUrl: `/download/apk/${batchId}/${dir}/${apkFile}`
            };

            console.log(`[APK Download] Adding APK file:`, apkFileInfo);
            apkFiles.push(apkFileInfo);
          } catch (fileErr) {
            console.error(`[APK Download] Error processing file ${apkFile}:`, fileErr);
          }
        }
      } catch (err) {
        console.error(`Error reading directory ${dir}:`, err);
      }
    }

    console.log(`[APK Download] Total APK files found: ${apkFiles.length}`);

    if (apkFiles.length === 0) {
      console.log(`[APK Download] No APK files found in batch directories: ${batchDirs.join(', ')}`);
      throw error(404, `No APK files found in batch ${batchId}. Found ${batchDirs.length} directories but no valid APK files.`);
    }

    // Sort by creation time (newest first)
    apkFiles.sort((a, b) => b.created.getTime() - a.created.getTime());

    return {
      batchId,
      apkFiles,
      totalFiles: apkFiles.length,
      batchCreated: new Date(parseInt(timestamp))
    };
  } catch (err) {
    console.error('Error loading batch APK files:', err);
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    throw error(500, 'Error loading APK files');
  }
}
