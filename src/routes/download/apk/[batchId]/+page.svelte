<script>
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  export let data;

  let downloading = {};
  let downloadedCount = 0;

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function formatDate(date) {
    return new Date(date).toLocaleString();
  }

  async function downloadFile(apkFile) {
    if (downloading[apkFile.filename]) return;

    downloading[apkFile.filename] = true;
    downloading = { ...downloading }; // Trigger reactivity

    try {
      const response = await fetch(apkFile.downloadUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = apkFile.filename;
      document.body.appendChild(a);
      a.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      downloadedCount++;
      console.log(`Successfully downloaded ${apkFile.filename}`);
    } catch (error) {
      console.error(`Error downloading ${apkFile.filename}:`, error);
      alert(`Error downloading ${apkFile.filename}: ${error.message}`);
    } finally {
      downloading[apkFile.filename] = false;
      downloading = { ...downloading }; // Trigger reactivity
    }
  }

  async function downloadAll() {
    if (data.apkFiles.length === 0) return;

    // Download files with a small delay between each to avoid overwhelming the browser
    for (let i = 0; i < data.apkFiles.length; i++) {
      const apkFile = data.apkFiles[i];
      
      if (!downloading[apkFile.filename]) {
        downloadFile(apkFile);
        
        // Add a small delay between downloads
        if (i < data.apkFiles.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }
  }

  onMount(() => {
    // Auto-refresh the page every 30 seconds to check for new files
    const interval = setInterval(() => {
      if (browser) {
        window.location.reload();
      }
    }, 30000);

    return () => clearInterval(interval);
  });
</script>

<svelte:head>
  <title>Download APK Files - Batch {data.batchId}</title>
</svelte:head>

<div class="download-page">
  <div class="download-header">
    <h1>APK Download Center</h1>
    <div class="batch-info">
      <p><strong>Batch ID:</strong> {data.batchId}</p>
      <p><strong>Generated:</strong> {formatDate(data.batchCreated)}</p>
      <p><strong>Total Files:</strong> {data.totalFiles}</p>
    </div>
  </div>

  {#if data.apkFiles.length > 0}
    <div class="download-actions">
      <button 
        class="download-all-btn" 
        on:click={downloadAll}
        disabled={Object.values(downloading).some(d => d)}
      >
        {#if Object.values(downloading).some(d => d)}
          Downloading... ({downloadedCount}/{data.totalFiles})
        {:else}
          Download All APKs
        {/if}
      </button>
    </div>

    <div class="apk-list">
      {#each data.apkFiles as apkFile}
        <div class="apk-item">
          <div class="apk-info">
            <h3>{apkFile.filename}</h3>
            <div class="apk-details">
              <span class="device-id">Device: {apkFile.deviceId}</span>
              <span class="file-size">{formatFileSize(apkFile.size)}</span>
              <span class="created-date">{formatDate(apkFile.created)}</span>
            </div>
          </div>
          <div class="apk-actions">
            <button 
              class="download-btn"
              on:click={() => downloadFile(apkFile)}
              disabled={downloading[apkFile.filename]}
            >
              {#if downloading[apkFile.filename]}
                <span class="spinner"></span>
                Downloading...
              {:else}
                Download
              {/if}
            </button>
          </div>
        </div>
      {/each}
    </div>
  {:else}
    <div class="no-files">
      <h2>No APK files found</h2>
      <p>The batch may still be processing or the files may have expired.</p>
      <button on:click={() => window.location.reload()}>Refresh Page</button>
    </div>
  {/if}
</div>

<style>
  .download-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .download-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
  }

  .download-header h1 {
    color: #333;
    margin-bottom: 1rem;
  }

  .batch-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
  }

  .batch-info p {
    margin: 0;
    color: #666;
  }

  .download-actions {
    text-align: center;
    margin-bottom: 2rem;
  }

  .download-all-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .download-all-btn:hover:not(:disabled) {
    background: #0056b3;
  }

  .download-all-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }

  .apk-list {
    display: grid;
    gap: 1rem;
  }

  .apk-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .apk-info h3 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-size: 1.1rem;
  }

  .apk-details {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: #666;
  }

  .device-id {
    font-weight: 600;
    color: #007bff;
  }

  .download-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s;
  }

  .download-btn:hover:not(:disabled) {
    background: #218838;
  }

  .download-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }

  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .no-files {
    text-align: center;
    padding: 3rem;
    color: #666;
  }

  .no-files button {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 1rem;
  }

  @media (max-width: 768px) {
    .download-page {
      padding: 1rem;
    }

    .batch-info {
      flex-direction: column;
      gap: 0.5rem;
    }

    .apk-item {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .apk-details {
      flex-direction: column;
      gap: 0.25rem;
    }
  }
</style>
