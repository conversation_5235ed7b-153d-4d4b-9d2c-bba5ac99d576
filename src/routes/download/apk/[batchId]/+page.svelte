<script>
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  export let data;

  let downloading = {};
  let downloadedCount = 0;

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function formatDate(date) {
    return new Date(date).toLocaleString();
  }

  async function downloadFile(apkFile) {
    if (downloading[apkFile.filename]) return;

    downloading[apkFile.filename] = true;
    downloading = { ...downloading }; // Trigger reactivity

    try {
      const response = await fetch(apkFile.downloadUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = apkFile.filename;
      document.body.appendChild(a);
      a.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      downloadedCount++;
      console.log(`Successfully downloaded ${apkFile.filename}`);
    } catch (error) {
      console.error(`Error downloading ${apkFile.filename}:`, error);
      alert(`Error downloading ${apkFile.filename}: ${error.message}`);
    } finally {
      downloading[apkFile.filename] = false;
      downloading = { ...downloading }; // Trigger reactivity
    }
  }

  async function downloadAll() {
    if (data.apkFiles.length === 0) return;

    // Download files with a small delay between each to avoid overwhelming the browser
    for (let i = 0; i < data.apkFiles.length; i++) {
      const apkFile = data.apkFiles[i];

      if (!downloading[apkFile.filename]) {
        downloadFile(apkFile);

        // Add a small delay between downloads
        if (i < data.apkFiles.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }
    }
  }

  onMount(() => {
    // Auto-refresh the page every 30 seconds to check for new files
    const interval = setInterval(() => {
      if (browser) {
        window.location.reload();
      }
    }, 30000);

    return () => clearInterval(interval);
  });
</script>

<svelte:head>
  <title>Download APK Files - Batch {data.batchId}</title>
</svelte:head>

<div class="admin-container">
  <header class="admin-header">
    <div class="admin-title-container">
      {#if data.apkFiles.length > 0}
        <div class="download-actions">
          <button
            class="button button-blue"
            on:click={downloadAll}
            disabled={Object.values(downloading).some((d) => d)}
          >
            {#if Object.values(downloading).some((d) => d)}
              Downloading... ({downloadedCount}/{data.totalFiles})
            {:else}
              Download All APKs
            {/if}
          </button>
        </div>
      {/if}
    </div>
    <div class="batch-info">
      <span><strong>Batch ID:</strong> {data.batchId}</span>
      <span><strong>Generated:</strong> {formatDate(data.batchCreated)}</span>
      <span><strong>Total Files:</strong> {data.totalFiles}</span>
    </div>
  </header>

  {#if data.apkFiles.length > 0}
    <div class="apk-list">
      {#each data.apkFiles as apkFile}
        <div class="apk-item">
          <div class="apk-info">
            <h3>{apkFile.serviceName || 'Unknown APK'}</h3>
            <div class="apk-details">
              <span class="device-id">Device: {apkFile.deviceId}</span>
              <span class="file-size">{formatFileSize(apkFile.size)}</span>
              <span class="created-date">{formatDate(apkFile.created)}</span>
              <span class="filename"
                >File: {apkFile.friendlyFilename || apkFile.filename}</span
              >
            </div>
          </div>
          <div class="apk-actions">
            <button
              class="download-btn"
              on:click={() => downloadFile(apkFile)}
              disabled={downloading[apkFile.filename]}
            >
              {#if downloading[apkFile.filename]}
                <span class="spinner"></span>
                Downloading...
              {:else}
                Download
              {/if}
            </button>
          </div>
        </div>
      {/each}
    </div>
  {:else}
    <div class="no-files">
      <h2>No APK files found</h2>
      <p>The batch may still be processing or the files may have expired.</p>
      <button on:click={() => window.location.reload()}>Refresh Page</button>
    </div>
  {/if}
</div>

<style>
  .batch-info {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
    color: var(--text-secondary);
    font-size: 0.9rem;
  }

  .batch-info span {
    margin: 0;
  }

  .download-actions {
    text-align: center;
    margin-bottom: var(--space-xl);
  }

  .apk-list {
    display: grid;
    gap: var(--space-md);
  }

  .apk-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background: var(--bg-card);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .apk-info h3 {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
  }

  .apk-details {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: var(--text-secondary);
  }

  .device-id {
    font-weight: 600;
    color: var(--primary-brand);
  }

  .filename {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-style: italic;
  }

  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .no-files {
    text-align: center;
    padding: 3rem;
    color: #666;
  }

  .no-files button {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 1rem;
  }

  @media (max-width: 768px) {
    .batch-info {
      flex-direction: column;
      gap: var(--space-sm);
    }

    .apk-item {
      flex-direction: column;
      align-items: stretch;
      gap: var(--space-md);
    }

    .apk-details {
      flex-direction: column;
      gap: var(--space-xs);
    }
  }
</style>
