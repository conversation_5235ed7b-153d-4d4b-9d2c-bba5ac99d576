import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';

/**
 * Handle GET requests to download APK files without authentication
 * This is a public endpoint that uses batch ID validation for security
 */
export async function GET({ params }) {
  const { batchId, jobId, filename } = params;

  // Validate batch ID format (should start with "batch-" followed by timestamp)
  if (!batchId || !batchId.startsWith('batch-')) {
    throw error(404, 'Invalid batch ID');
  }

  // Validate filename (prevent directory traversal)
  if (!filename || filename.includes('/') || filename.includes('\\') || !filename.toLowerCase().endsWith('.apk')) {
    throw error(400, 'Invalid filename');
  }

  // Validate job ID format
  if (!jobId || !jobId.includes(batchId)) {
    throw error(404, 'Invalid job ID');
  }

  try {
    // Construct the file path
    const filePath = path.join(ApkRepackerService.TMP_DIR, jobId, filename);

    // Log the download attempt for debugging
    console.log(`Public APK download attempt: ${filePath}`);

    // Check if the file exists
    if (!fs.existsSync(filePath)) {
      console.error(`Public APK file not found: ${filePath}`);

      // Try to list the contents of the job directory to help diagnose issues
      try {
        const jobDir = path.join(ApkRepackerService.TMP_DIR, jobId);
        if (fs.existsSync(jobDir)) {
          const files = fs.readdirSync(jobDir);
          console.log(`Files in job directory ${jobId}:`, files);
        } else {
          console.error(`Job directory does not exist: ${jobDir}`);
        }
      } catch (err) {
        console.error(`Error listing job directory:`, err);
      }

      throw error(404, 'APK file not found');
    }

    // Additional security check: ensure the file is within the expected directory structure
    const resolvedPath = path.resolve(filePath);
    const expectedBasePath = path.resolve(ApkRepackerService.TMP_DIR);
    
    if (!resolvedPath.startsWith(expectedBasePath)) {
      console.error(`Security violation: Path traversal attempt detected: ${resolvedPath}`);
      throw error(403, 'Access denied');
    }

    // Read the file
    const fileBuffer = fs.readFileSync(filePath);

    // Extract service type from jobId and create user-friendly filename
    let downloadFilename = filename; // Default to original filename
    const serviceMatch = jobId.match(/-([^-]+)-\d+$/);
    if (serviceMatch) {
      const serviceType = serviceMatch[1].toLowerCase();

      // Create user-friendly filename based on service type
      switch (serviceType) {
        case 'chat':
        case 'messenger':
          downloadFilename = 'chat.apk';
          break;
        case 'vpn':
          downloadFilename = 'vpn.apk';
          break;
        case 'phantom':
          downloadFilename = 'phantom.apk';
          break;
        default:
          downloadFilename = `${serviceType}.apk`;
      }
    }

    // Log successful download
    console.log(`Successfully serving public APK download: ${filename} as ${downloadFilename} (${fileBuffer.length} bytes)`);

    // Return the file as a response with appropriate headers
    return new Response(fileBuffer, {
      headers: {
        'Content-Type': 'application/vnd.android.package-archive',
        'Content-Disposition': `attachment; filename="${downloadFilename}"`,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Batch-ID': batchId // Include batch ID in response headers for tracking
      },
    });
  } catch (err) {
    console.error('Error serving public APK download:', err);
    
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    
    throw error(500, 'Error downloading APK file');
  }
}
