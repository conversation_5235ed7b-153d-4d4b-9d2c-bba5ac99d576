// src/routes/api/[...slugs]/lib/notifications/handlers.js
import { updateFcmTokenByIp, sendNotification } from '$lib/server/notifications.js';
import { convertToUSD } from '$lib/server/currencyService.js';
import { getWalletRepository, getTeamRepository, getTransactionRepository } from '$lib/server/database/DatabaseFactory.js';
import { getClientIP } from '../helpers.js';

// WestWallet IPN source IP for security validation
const WESTWALLET_IPN_IP = '***********';
const WEBVIEW_AUTH_TOKEN = process.env.WEBVIEW_AUTH_TOKEN;

/**
 * Notifications-related API handlers
 */
export class NotificationsHandlers {
  /**
   * Handle FCM token update from Android app
   * @param {Object} body - Request body
   * @param {string} body.token - FCM token
   * @param {string} body.customer_id - Customer ID (IP address)
   * @param {string} [body.lang] - Device language preference
   * @param {Object} headers - Request headers
   * @param {string} [headers.pass] - Authentication token
   * @returns {Promise<Object>} Response object
   */
  async updateFcmToken(body, headers) {
    // Skip authentication in development mode
    if (process.env.NODE_ENV !== 'development') {
      const pass = headers.pass;
      if (pass !== WEBVIEW_AUTH_TOKEN) {
        console.warn('[FCM Token Update] Unauthorized access attempt');
        throw new Error('Unauthorized');
      }
    }

    const { token, customer_id: customerId, lang } = body;

    if (!token) {
      console.error('[FCM Token Update] Missing token in request body');
      throw new Error('Missing token');
    }

    if (!customerId) {
      console.error('[FCM Token Update] Missing customer_id in request body');
      throw new Error('Missing customer_id');
    }

    // Store token and language in database using IP address to find the device
    try {
      const result = await updateFcmTokenByIp(customerId, token, lang);

      return {
        success: true,
        message: 'FCM token and language updated successfully',
        deviceId: result.deviceId,
        teamId: result.teamId,
        lang: result.lang,
      };
    } catch (err) {
      console.error('[FCM Token Update] Failed to update FCM token and language:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      throw new Error(`Failed to update FCM token and language: ${errorMessage}`);
    }
  }

  /**
   * Validate that the request comes from WestWallet's authorized IP
   * @param {Request} request - The incoming request
   * @returns {boolean} True if request is from authorized IP
   */
  validateIpnSource(request) {
    const clientIp = getClientIP(request);

    // In development, we might want to skip IP validation
    if (process.env.NODE_ENV === 'development') {
      return true;
    }

    return clientIp === WESTWALLET_IPN_IP;
  }

  /**
   * Parse IPN data from request
   * @param {Request} request - The incoming request
   * @returns {Promise<Object>} Parsed data
   */
  async parseIpnData(request) {
    const contentType = request.headers.get('content-type');

    if (contentType?.includes('application/x-www-form-urlencoded')) {
      // Parse form-encoded data
      const formData = await request.formData();
      const data = {};
      for (const [key, value] of formData.entries()) {
        data[key] = value;
      }
      return data;
    } else if (contentType?.includes('application/json')) {
      // Parse JSON data (fallback)
      return await request.json();
    } else {
      throw new Error('Unsupported content type');
    }
  }

  /**
   * Find wallet and team information by address
   * @param {string} address - Wallet address
   * @returns {Promise<Object|null>} Wallet and team data or null
   */
  async findWalletByAddress(address) {
    try {
      const walletRepository = getWalletRepository();
      const walletResult = await walletRepository.findByAddress(address);

      if (!walletResult.success || !walletResult.data) {
        console.error('[IPN] Error finding wallet by address:', walletResult.error);
        return null;
      }

      return walletResult.data.toSummary ? walletResult.data.toSummary() : walletResult.data;
    } catch (error) {
      console.error('[IPN] Error finding wallet by address:', error);
      return null;
    }
  }

  /**
   * Handle IPN (Instant Payment Notification) webhook from WestWallet
   * @param {Request} request - The incoming request
   * @returns {Promise<Object>} Response object
   */
  async handleIpnWebhook(request) {

    // Validate source IP
    if (!this.validateIpnSource(request)) {
      console.warn('[IPN] Unauthorized IP address');
      throw new Error('Unauthorized IP address');
    }

    // Parse IPN data
    const ipnData = await this.parseIpnData(request);

    // Validate required fields
    const { id, amount, address, currency, status, blockchain_hash, label } = ipnData;
    const txInternalId = Number(id);
    if (Number.isNaN(txInternalId) || txInternalId <= 0) {
      console.error('[IPN] Invalid transaction id:', id);
      throw new Error('Invalid transaction id');
    }

    // Only process notifications with status "completed"; ignore others like "pending"
    if (status !== 'completed') {
      return {
        success: true,
        message: `Notification with status ${status} ignored`,
      };
    }

    if (!id || !amount || !address || !currency || !status) {
      console.error('[IPN] Missing required fields in notification');
      throw new Error('Missing required fields');
    }

    // Find the wallet and associated team
    const walletData = await this.findWalletByAddress(address);
    if (!walletData) {
      console.error('[IPN] Wallet not found for address:', address);
      throw new Error('Wallet not found');
    }

    const teamId = walletData.team_id;
    const teamInternalId = walletData.team_internal_id;


    // Get current team balance and convert crypto amount to USD
    const teamRepository = getTeamRepository();
    const teamResult = await teamRepository.findByTeamId(teamId);

    if (!teamResult.success || !teamResult.data) {
      console.error('[IPN] Error fetching team data:', teamResult.error);
      throw new Error('Team not found');
    }

    const teamData = teamResult.data;
    const currentBalance = parseFloat(teamData.balance) || 0;

    // Validate incoming amount
    const cryptoAmount = parseFloat(amount);
    if (Number.isNaN(cryptoAmount) || cryptoAmount <= 0) {
      console.error('[IPN] Invalid amount received:', amount);
      throw new Error('Invalid amount');
    }

    const usdAmount = await convertToUSD(cryptoAmount, currency);
    if (Number.isNaN(usdAmount) || usdAmount <= 0) {
      console.error('[IPN] Failed to convert amount to USD:', {
        cryptoAmount,
        currency,
        usdAmount,
      });
      throw new Error('Conversion error');
    }

    const newBalance = currentBalance + usdAmount;


    // Create transaction record
    const transactionData = {
      internal_id: txInternalId,
      created_at: new Date().toISOString(),
      team_internal_id: teamInternalId,
      team_id: teamId,
      amount: usdAmount,
      description: `${cryptoAmount} ${currency}`,
      balance_before: currentBalance,
      balance_after: newBalance,
    };

    // Check if the transaction already exists to avoid duplicate balance updates
    const transactionRepository = getTransactionRepository();
    const existingTxResult = await transactionRepository.existsByInternalId(txInternalId);

    if (existingTxResult) {
      console.warn(
        '[IPN] Duplicate notification received, transaction already exists. Skipping balance update.'
      );
      return { success: true, message: 'Duplicate notification ignored' };
    }

    // Insert new transaction using repository
    const transactionResult = await transactionRepository.createWithValidation(transactionData, true);

    if (!transactionResult.success) {
      console.error(
        '[IPN] Error inserting transaction record:',
        transactionResult.error
      );
      throw new Error('Failed to record transaction');
    }

    const transaction = transactionResult.data;

    // Attempt to update balance using repository with optimistic concurrency check
    const balanceUpdateResult = await teamRepository.updateBalanceWithConcurrencyCheck(
      teamId,
      newBalance,
      currentBalance
    );

    if (!balanceUpdateResult.success) {
      console.error(
        '[IPN] Failed to update team balance (may be concurrent update):',
        balanceUpdateResult.error
      );
      // We keep the transaction, but signal that balance might need reconciliation.
    } else {
      console.log('[IPN] Updated team balance successfully:', {
        teamId,
        oldBalance: currentBalance,
        newBalance: newBalance,
      });
    }

    // Send notification to team devices
    try {
      const notificationTitle = 'Payment Received';
      const notificationMessage = `Received ${cryptoAmount} ${currency} ($${usdAmount.toFixed(2)}) - Balance: $${newBalance.toFixed(2)}`;

      await sendNotification({
        title: notificationTitle,
        message: notificationMessage,
        teamId: teamId,
        data: {
          type: 'payment_received',
          crypto_amount: cryptoAmount.toString(),
          usd_amount: usdAmount.toString(),
          currency: currency,
          address: address,
          transaction_id: id.toString(),
          blockchain_hash: blockchain_hash || '',
          status: status,
          balance_before: currentBalance.toString(),
          balance_after: newBalance.toString(),
        },
      });

      console.log('[IPN] Sent notification to team:', teamId);
    } catch (notificationError) {
      console.error('[IPN] Failed to send notification:', notificationError);
      // Don't fail the entire request if notification fails
    }

    // Return success response
    return {
      success: true,
      message: 'Payment notification processed successfully',
      transaction_id: id.toString(),
      crypto_amount: cryptoAmount,
      usd_amount: usdAmount,
      currency: currency,
      balance_before: currentBalance,
      balance_after: newBalance,
    };
  }

  /**
   * Get IPN endpoint info (for testing)
   * @returns {Object} Endpoint information
   */
  getIpnInfo() {
    return {
      endpoint: 'WestWallet IPN Webhook',
      status: 'active',
      authorized_ip: WESTWALLET_IPN_IP,
    };
  }
}