import { json } from '@sveltejs/kit';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';
import fs from 'fs';
import path from 'path';

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
  const batchId = url.searchParams.get('batchId');
  
  try {
    const tmpDir = ApkRepackerService.TMP_DIR;
    
    const result = {
      tmpDir,
      exists: fs.existsSync(tmpDir),
      directories: [],
      batchId,
      matchingDirs: []
    };
    
    if (fs.existsSync(tmpDir)) {
      const dirs = fs.readdirSync(tmpDir);
      result.directories = dirs;
      
      if (batchId) {
        result.matchingDirs = dirs.filter(dir => dir.includes(batchId));
        
        // Get details of matching directories
        result.dirDetails = result.matchingDirs.map(dir => {
          const dirPath = path.join(tmpDir, dir);
          try {
            const files = fs.readdirSync(dirPath);
            return {
              name: dir,
              files: files,
              apkFiles: files.filter(f => f.toLowerCase().endsWith('.apk'))
            };
          } catch (err) {
            return {
              name: dir,
              error: err.message
            };
          }
        });
      }
    }
    
    return json(result);
  } catch (error) {
    return json({ error: error.message }, { status: 500 });
  }
}
