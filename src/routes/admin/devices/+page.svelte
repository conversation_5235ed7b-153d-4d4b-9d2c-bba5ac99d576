<script>
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';

  export let data;
  let devices = [];
  let error = null;
  let totalDevices = 0;
  let recentDevice = null;

  const getRoleBadgeClass = (role) => {
    if (!role) return 'role-badge';
    switch (role.toLowerCase()) {
      case 'admin':
        return 'role-badge role-admin';
      case 'user':
        return 'role-badge role-user';
      default:
        return 'role-badge';
    }
  };

  $: if (data) {
    devices = data.devices || [];
    error = data.error || null;
    totalDevices = data.totalDevices || 0;
    recentDevice = data.recentDevice || null;
  }

  function timeAgo(dateString) {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const seconds = Math.floor(diffMs / 1000);
      let interval = Math.floor(seconds / 31536000);
      if (interval > 1) return interval + ' years ago';
      interval = Math.floor(seconds / 2592000);
      if (interval > 1) return interval + ' months ago';
      interval = Math.floor(seconds / 86400);
      if (interval > 1) return interval + ' days ago';
      interval = Math.floor(seconds / 3600);
      if (interval > 1) return interval + ' hours ago';
      interval = Math.floor(seconds / 60);
      if (interval > 1) return interval + ' minutes ago';
      return Math.floor(seconds) + ' seconds ago';
    } catch {
      return 'Unknown time';
    }
  }

  function handleEdit(device) {
    goto(`/admin/devices/${device.internal_id}`);
  }

  function handleAddDevice() {
    goto('/admin/devices/new');
  }

  let generatingApkFor = null;
  let apkGenerationError = null;
  let apkDownloadUrl = null;
  let apkFilename = '';
  let generatingAllApks = false;
  let allApksError = null;

  async function handleGenerateApk(device) {
    if (generatingApkFor === device.internal_id) return; // Prevent multiple clicks

    generatingApkFor = device.internal_id;
    apkGenerationError = null;
    apkDownloadUrl = null;

    try {
      // Get CSRF token from the page data
      const csrfToken = data.csrfToken;
      if (!csrfToken) {
        throw new Error('CSRF token not found');
      }

      const formData = new FormData();
      formData.append('deviceId', device.internal_id);
      formData.append('csrfToken', csrfToken);

      const response = await fetch('?/generateDeviceApk', {
        method: 'POST',
        body: formData,
        headers: {
          Accept: 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        apkDownloadUrl = result.downloadUrl;
        apkFilename =
          result.filename ||
          `phantom-${device.nickname || device.internal_id}.apk`;

        // Trigger download automatically
        if (apkDownloadUrl) {
          const a = document.createElement('a');
          a.href = apkDownloadUrl;
          a.download = apkFilename;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        }
      } else {
        apkGenerationError = result.error || 'Failed to generate APK';
      }
    } catch (error) {
      console.error('Error generating APK:', error);
      apkGenerationError =
        error.message || 'An error occurred while generating the APK';
    } finally {
      generatingApkFor = null;
    }
  }

  async function handleGenerateAllApks() {
    if (generatingAllApks) return; // Prevent multiple clicks

    generatingAllApks = true;
    allApksError = null;

    try {
      // Get CSRF token from the page data
      const csrfToken = data.csrfToken;
      if (!csrfToken) {
        throw new Error('CSRF token not found');
      }

      const formData = new FormData();
      formData.append('csrfToken', csrfToken);

      const response = await fetch('?/generateAllApks', {
        method: 'POST',
        body: formData,
        headers: {
          Accept: 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        // Redirect to the download page
        const downloadUrl = `/download/apk/${result.batchId}`;
        window.open(downloadUrl, '_blank');
      } else {
        allApksError = result.error || 'Failed to generate APKs';
      }
    } catch (error) {
      console.error('Error generating all APKs:', error);
      allApksError =
        error.message || 'An error occurred while generating APKs';
    } finally {
      generatingAllApks = false;
    }
  }
</script>

<svelte:head>
  <title>Phantom - Admin Panel | Devices</title>
</svelte:head>

<div class="admin-card">
  <div class="admin-card-header">
    <div class="admin-card-title">Devices</div>
    <div class="batch-actions">
      <button
        class="button generate-all-button"
        on:click={handleGenerateAllApks}
        disabled={generatingAllApks}
      >
        {#if generatingAllApks}
          <span class="spinner spinner-small"></span>
          Generating APKs...
        {:else}
          Generate All APKs
        {/if}
      </button>
      <button class="button add-team-button" on:click={handleAddDevice}
        >Add Device</button
      >
    </div>
  </div>
  <div class="admin-stats-compact">
    <div class="stats-summary">
      <div class="stat-item">
        <span class="stat-label">Total Devices:</span>
        <span class="stat-value-compact text-primary"
          >{totalDevices}</span
        >
      </div>
    </div>
  </div>

  {#if error}
    <div class="alert alert-error">
      <div class="alert-content">
        <div class="alert-title">Error</div>
        <div class="alert-message">{error}</div>
      </div>
    </div>
  {/if}

  {#if allApksError}
    <div class="alert alert-error">
      <div class="alert-content">
        <div class="alert-title">APK Generation Error</div>
        <div class="alert-message">{allApksError}</div>
      </div>
    </div>
  {/if}

  {#if devices.length === 0}
    <div class="empty-state">
      <p>No devices found. Add a device to get started.</p>
    </div>
  {:else}
    <table class="client-table">
      <thead>
        <tr>
          <th class="table-header">Actions</th>
          <th class="table-header">IP</th>
          <th class="table-header">Nickname</th>
          <th class="table-header">Role</th>
          <th class="table-header">Language</th>
          <th class="table-header">Team ID</th>
          <th class="table-header">Last Active</th>
          <th class="table-header">Created</th>
        </tr>
      </thead>
      <tbody>
        {#each devices as device}
          <tr>
            <td class="table-cell">
              <div class="button-group">
                <form
                  method="POST"
                  action="?/delete"
                  use:enhance={({ cancel }) => {
                    if (
                      !confirm('Are you sure you want to delete this device?')
                    ) {
                      cancel();
                      return;
                    }

                    return async ({ result }) => {
                      if (result.type === 'success') {
                        invalidateAll();
                      } else if (result.type === 'error') {
                        console.error('Delete failed:', result.error);
                        error =
                          result.error?.message || 'Failed to delete device';
                      }
                    };
                  }}
                >
                  <input type="hidden" name="id" value={device.internal_id} />
                  <button
                    class="button button-small button-danger"
                    type="submit"
                  >
                    Delete
                  </button>
                </form>
                <button
                  class="button button-small"
                  on:click={() => handleEdit(device)}
                >
                  Edit
                </button>
                <button
                  class="button-icon apk-button {generatingApkFor === device.internal_id ? 'button-loading' : ''}"
                  on:click={() => handleGenerateApk(device)}
                  title="Generate APK with device configs"
                  disabled={generatingApkFor === device.internal_id}
                >
                  {#if generatingApkFor === device.internal_id}
                    <span class="spinner spinner-small"></span>
                  {:else}
                    <img
                      src="/apk.webp"
                      alt="Generate APK"
                      class="apk-icon"
                    />
                  {/if}
                </button>
                {#if apkGenerationError && generatingApkFor === device.internal_id}
                  <div class="error-message">
                    {apkGenerationError}
                  </div>
                {/if}
              </div>
            </td>
            <td class="table-cell text-warning font-bold"
              >{device.ip || '-'}</td
            >
            <td class="table-cell">{device.nickname || '-'}</td>
            <td class="table-cell">
              {#if device.role}
                <span class={getRoleBadgeClass(device.role)}>{device.role}</span
                >
              {:else}
                <span class="text-muted">-</span>
              {/if}
            </td>
            <td class="table-cell">{device.lang || '-'}</td>
            <td class="table-cell">
              {#if device.team_id}
                <a
                  href="/admin/teams/{device.team_id}"
                  class="text-link"
                  target="_blank"
                  rel="noopener noreferrer">{device.team_id}</a
                >
              {:else}
                -
              {/if}
            </td>
            <td style="padding: 4px 4px;"
              >{device.last_auth_at
                ? timeAgo(device.last_auth_at)
                : 'Never'}</td
            >
            <td class="table-cell">{timeAgo(device.created_at)}</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>

<style>
  .generate-all-button {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
  }

  .generate-all-button:hover:not(:disabled) {
    background-color: #218838;
    border-color: #1e7e34;
  }

  .generate-all-button:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
  }

  .spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 8px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .batch-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }
</style>
