import { dev } from '$app/environment';
import { getSession, verifySession } from '$lib/server/session';
import { verifyCsrfToken } from '$lib/server/csrf';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';
import fs from 'fs';
import path from 'path';

/** @type {import('./$types').PageServerLoad} */
export async function load({ parent }) {
  // Clean up completed jobs (older than 1 hour) when the page loads
  try {
    const completedJobsRemoved = ApkRepackerService.cleanupCompletedJobs();
    if (completedJobsRemoved > 0) {
      console.log(
        `Cleaned up ${completedJobsRemoved} completed APK repacking jobs on page load`
      );
    }

    // Also clean up very old files (older than 24 hours)
    const oldFilesRemoved = ApkRepackerService.cleanupTempFiles();
    if (oldFilesRemoved > 0) {
      console.log(
        `Cleaned up ${oldFilesRemoved} old APK repacking directories on page load`
      );
    }
  } catch (error) {
    console.error('Error cleaning up temporary files:', error);
  }

  // Get list of available APK templates
  const templatesDir = path.join(process.cwd(), '../apk-templates');

  let templates = [];

  try {
    if (fs.existsSync(templatesDir)) {
      const files = fs.readdirSync(templatesDir);
      templates = files
        .filter((file) => file.toLowerCase().endsWith('.apk'))
        .map((file) => ({
          filename: file,
          path: path.join('../apk-templates', file),
          size: fs.statSync(path.join(templatesDir, file)).size,
        }));

      console.log(`Found ${templates.length} APK templates in ${templatesDir}`);
    } else {
      console.warn(`Templates directory not found: ${templatesDir}`);
    }
  } catch (error) {
    console.error('Error reading APK templates directory:', error);
  }

  return {
    templates,
  };
}

/** @type {import('./$types').Actions} */
export const actions = {
  // Repack APK action
  repackApk: async ({ request, cookies }) => {
    // Clean up completed jobs before starting a new one
    try {
      const completedJobsRemoved = ApkRepackerService.cleanupCompletedJobs();
      if (completedJobsRemoved > 0) {
        console.log(
          `Cleaned up ${completedJobsRemoved} completed APK repacking jobs before starting new job`
        );
      }
    } catch (error) {
      console.error('Error cleaning up completed jobs:', error);
    }

    const data = await request.formData();
    const csrfToken = data.get('csrfToken');
    const config = data.get('config');
    const templatePath = data.get('templatePath');
    const assetFilename = data.get('assetFilename') || 'vpn.conf';
    const componentId = data.get('componentId'); // Get the component ID for creating a unique job ID
    const submissionId = data.get('submissionId'); // Get the submission ID for tracking

    console.log(
      `Processing APK repacking request for submission: ${submissionId || 'unknown'}, component: ${componentId || 'unknown'}`
    );

    // Log the request parameters for debugging
    console.log('APK repacking request parameters:', {
      templatePath,
      assetFilename,
      componentId,
      submissionId,
      configLength: config ? String(config).length : 0,
    });

    // Verify the session
    const signedSession = cookies.get('admin_session');
    /** @type {string|null} */
    const sessionId = verifySession(signedSession);
    const session = sessionId ? getSession(sessionId) : null;

    // If not authenticated or CSRF token is invalid, return error
    if (!dev && !session) {
      return { success: false, error: 'Authentication required' };
    }

    // Verify CSRF token
    if (!dev && !sessionId) {
      return { success: false, error: 'Invalid session' };
    }

    if (csrfToken) {
      const tokenStr =
        typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
      if (!dev && !verifyCsrfToken(tokenStr, sessionId)) {
        return { success: false, error: 'Invalid request token' };
      }
    } else {
      return { success: false, error: 'CSRF token required' };
    }

    // Validate inputs
    if (!templatePath) {
      return { success: false, error: 'APK template selection is required' };
    }

    // Validate the template path (prevent directory traversal)
    const templatePathStr =
      typeof templatePath === 'string' ? templatePath : String(templatePath);
    if (!templatePathStr.startsWith('../apk-templates/')) {
      return { success: false, error: 'Invalid template path' };
    }

    // Check if the template file exists
    const fullTemplatePath = path.join(process.cwd(), templatePathStr);
    if (!fs.existsSync(fullTemplatePath)) {
      return { success: false, error: 'Selected template file does not exist' };
    }

    // Check if the file has a valid APK extension
    if (!fullTemplatePath.toLowerCase().endsWith('.apk')) {
      return {
        success: false,
        error: 'Selected template must be an APK file (.apk extension)',
      };
    }

    // Get file stats
    const stats = fs.statSync(fullTemplatePath);

    // Check if the file has content
    if (stats.size === 0) {
      return { success: false, error: 'The selected APK template is empty' };
    }

    // Check if the file size is reasonable (at least 1MB for a valid APK)
    if (stats.size < 1024 * 1024) {
      return {
        success: false,
        error: 'The selected APK template is too small',
        details: `File size: ${(stats.size / 1024).toFixed(2)} KB. A valid APK file should be at least 1MB.`,
      };
    }

    if (!config) {
      return { success: false, error: 'Configuration is required' };
    }

    // Read the template file into a buffer
    const apkBuffer = fs.readFileSync(fullTemplatePath);

    // Extract the template filename without extension
    const templateFilename = path.basename(fullTemplatePath, '.apk');

    // Log template information
    console.log('Using APK template:', {
      path: templatePathStr,
      size: stats.size,
      filename: path.basename(fullTemplatePath),
      baseFilename: templateFilename,
    });

    // Use the APK Repacker Service to create a new repacking job
    const configStr = typeof config === 'string' ? config : String(config);
    const assetFilenameStr =
      typeof assetFilename === 'string' ? assetFilename : String(assetFilename);

    // Create a custom job ID using the component ID if available
    let customJobId = undefined; // Use undefined instead of null to match the expected type
    if (componentId) {
      // Use the component ID as part of the job ID to ensure uniqueness
      customJobId = `${Date.now()}-${componentId}`;
      console.log(
        `Creating repacking job with custom ID: ${customJobId} for submission: ${submissionId || 'unknown'}`
      );
    }

    // Create a custom APK buffer with a name property to pass the template name
    const namedApkBuffer = apkBuffer;
    // @ts-ignore - Adding a custom property to the buffer
    namedApkBuffer.name = templateFilename + '.apk';

    console.log(
      `Calling ApkRepackerService.createRepackingJob for submission: ${submissionId || 'unknown'}`
    );
    const result = await ApkRepackerService.createRepackingJob(
      namedApkBuffer,
      configStr,
      assetFilenameStr,
      customJobId
    );

    // Add submission ID to the result for tracking
    const resultWithSubmissionId = {
      ...result,
      submissionId: submissionId || undefined,
    };

    console.log(
      `APK repacking result for submission ${submissionId || 'unknown'}:`,
      {
        success: resultWithSubmissionId.success,
        jobId: resultWithSubmissionId.jobId,
        apkName: resultWithSubmissionId.apkName,
        error: resultWithSubmissionId.error,
        details: resultWithSubmissionId.details,
      }
    );

    // Return the result with submission ID
    return resultWithSubmissionId;
  },
};
