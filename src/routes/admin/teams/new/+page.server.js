import { fail, redirect } from '@sveltejs/kit';
import { getTeamRepository } from '$lib/server/database/DatabaseFactory.js';
import { v4 as uuidv4 } from 'uuid';
import { generateTeamWallets } from '$lib/server/walletService.js';

// SvelteKit page server for /team/new (no server data needed)
export const load = async () => {
  return {};
};

export const actions = {
  default: async ({ request }) => {
    const formData = await request.formData();
    const id = formData.get('id');
    const charge_amount = formData.get('charge_amount');
    const balance = formData.get('balance');
    const teamRepository = getTeamRepository();

    // Validate required fields
    if (!id || typeof charge_amount === 'undefined' || charge_amount === null || typeof balance === 'undefined' || balance === null) {
      return fail(400, {
        success: false,
        error: 'Missing required fields: id, charge_amount, balance',
      });
    }

    // Auto-generate internal_id and created_at if not provided
    const now = new Date().toISOString();
    const nextChargeDate = new Date(now);
    nextChargeDate.setDate(nextChargeDate.getDate() + 30);
    const nextChargeAt = nextChargeDate.toISOString();
    const team = {
      internal_id: formData.get('internal_id') || uuidv4(),
      id: id.toString(), // Ensure id is a string
      created_at: formData.get('created_at') || now,
      next_charge_at: nextChargeAt,
      charge_amount: parseFloat(charge_amount.toString()), // Ensure charge_amount is a number
      balance: parseFloat(balance.toString()), // Ensure balance is a number
      owner_internal_id: formData.get('owner_internal_id') || null,
      owner_id: formData.get('owner_id') || null,
    };

    const createResult = await teamRepository.create(team);

    if (!createResult.success) {
      return fail(400, { success: false, error: createResult.error?.message || 'Failed to create team' });
    }

    const createdTeam = createResult.data;

    // Generate wallets for the new team
    try {
      await generateTeamWallets(createdTeam.internal_id, createdTeam.id);
    } catch (walletError) {
      console.error(
        `Failed to generate wallets for team ${createdTeam.id}:`,
        walletError
      );
      // Don't fail the team creation if wallet generation fails
      // The team is created successfully, wallets can be generated later
    }

    throw redirect(303, `/admin/teams/`);
    //throw redirect(303, `/admin/teams/${createdTeam.id}`);
  },
};
