// src/lib/server/cron/utils/notificationTemplates.js

/**
 * Notification templates for billing system
 */

import { formatDateForLogging } from './dateHelpers.js';

// Localization strings for notifications
const notificationLocales = {
  en: {
    upcoming_charge_title: "Upcoming Charge Reminder",
    upcoming_charge_message: (amount, date) => `Your account will be charged ${amount} in 2 days on ${date}. Please ensure sufficient balance.`,
    charge_tomorrow_title: "Charge Tomorrow",
    charge_tomorrow_message: (amount, date) => `Your account will be charged ${amount} tomorrow on ${date}. Please ensure sufficient balance to maintain service.`,
    payment_processed_title: "Payment Processed",
    payment_processed_message: (amount, balance, nextCharge) => `${amount} has been charged to your account. Current balance: ${balance}. Next charge: ${nextCharge}.`,
    insufficient_funds_title: "Insufficient Funds - Service Suspended",
    insufficient_funds_message: (amount, balance, shortfall) => `Unable to charge ${amount}. Current balance: ${balance}. Please add ${shortfall} to restore service.`,
    service_restored_title: "Service Restored",
    service_restored_message: (balance) => `Your service has been restored. Current balance: ${balance}.`,
    billing_error_title: "Billing System Error",
    billing_error_message: (teamId, error, operation) => `Error processing ${operation} for team ${teamId}: ${error}`
  },
  ru: {
    upcoming_charge_title: "Напоминание о предстоящем списании",
    upcoming_charge_message: (amount, date) => `С вашего счета будет списано ${amount} через 2 дня, ${date}. Пожалуйста, пополните баланс заранее.`,
    charge_tomorrow_title: "Списание завтра",
    charge_tomorrow_message: (amount, date) => `С вашего счета будет списано ${amount} завтра, ${date}. Пожалуйста, пополните баланс для продолжения обслуживания.`,
    payment_processed_title: "Платеж выполнен",
    payment_processed_message: (amount, balance, nextCharge) => `${amount} успешно списано с вашего счета. Текущий баланс: ${balance}. Следующее списание: ${nextCharge}.`,
    insufficient_funds_title: "Недостаточно средств - сервис приостановлен",
    insufficient_funds_message: (amount, balance, shortfall) => `Не удалось списать ${amount}. Текущий баланс: ${balance}. Пополните на ${shortfall} для восстановления сервиса.`,
    service_restored_title: "Сервис восстановлен",
    service_restored_message: (balance) => `Ваш сервис восстановлен. Текущий баланс: ${balance}.`,
    billing_error_title: "Ошибка биллинга",
    billing_error_message: (teamId, error, operation) => `Ошибка при обработке ${operation} для команды ${teamId}: ${error}`
  },
  ua: {
    upcoming_charge_title: "Нагадування про майбутнє списання",
    upcoming_charge_message: (amount, date) => `З вашого рахунку буде списано ${amount} через 2 дні, ${date}. Будь ласка, поповніть баланс заздалегідь.`,
    charge_tomorrow_title: "Списання завтра",
    charge_tomorrow_message: (amount, date) => `З вашого рахунку буде списано ${amount} завтра, ${date}. Будь ласка, поповніть баланс для продовження обслуговування.`,
    payment_processed_title: "Платіж виконано",
    payment_processed_message: (amount, balance, nextCharge) => `${amount} успішно списано з вашого рахунку. Поточний баланс: ${balance}. Наступне списання: ${nextCharge}.`,
    insufficient_funds_title: "Недостатньо коштів - сервіс призупинено",
    insufficient_funds_message: (amount, balance, shortfall) => `Не вдалося списати ${amount}. Поточний баланс: ${balance}. Поповніть на ${shortfall} для відновлення сервісу.`,
    service_restored_title: "Сервіс відновлено",
    service_restored_message: (balance) => `Ваш сервіс відновлено. Поточний баланс: ${balance}.`,
    billing_error_title: "Помилка білінгу",
    billing_error_message: (teamId, error, operation) => `Помилка при обробці ${operation} для команди ${teamId}: ${error}`
  }
};

function getLocale(lang) {
  return notificationLocales[lang] || notificationLocales['en'];
}

/**
 * Two-day advance notification template
 * @param {Object} team - Team object
 * @param {Date} chargeDate - Date when charge will occur
 * @param {number} amount - Charge amount
 * @param {string} lang - Language code (en/ru/ua)
 * @returns {Object} Notification object
 */
export function getTwoDayAdvanceNotification(team, chargeDate, amount, lang = 'en') {
  const locale = getLocale(lang);
  const formattedDate = formatDateForLogging(chargeDate);
  const formattedAmount = `$${amount.toFixed(2)}`;
  return {
    title: locale.upcoming_charge_title,
    message: locale.upcoming_charge_message(formattedAmount, formattedDate),
    teamId: team.id,
    data: {
      type: "billing_reminder",
      days_until_charge: "2",
      charge_amount: amount.toString(),
      charge_date: chargeDate.toISOString(),
      team_id: team.id
    }
  };
}

/**
 * One-day advance notification template
 * @param {Object} team - Team object
 * @param {Date} chargeDate - Date when charge will occur
 * @param {number} amount - Charge amount
 * @param {string} lang - Language code (en/ru/ua)
 * @returns {Object} Notification object
 */
export function getOneDayAdvanceNotification(team, chargeDate, amount, lang = 'en') {
  const locale = getLocale(lang);
  const formattedDate = formatDateForLogging(chargeDate);
  const formattedAmount = `$${amount.toFixed(2)}`;
  return {
    title: locale.charge_tomorrow_title,
    message: locale.charge_tomorrow_message(formattedAmount, formattedDate),
    teamId: team.id,
    data: {
      type: "billing_reminder",
      days_until_charge: "1",
      charge_amount: amount.toString(),
      charge_date: chargeDate.toISOString(),
      team_id: team.id
    }
  };
}

/**
 * Successful charge notification template
 * @param {Object} team - Team object
 * @param {number} amount - Charged amount
 * @param {number} newBalance - New balance after charge
 * @param {Date} nextChargeDate - Next charge date
 * @param {string} lang - Language code (en/ru/ua)
 * @returns {Object} Notification object
 */
export function getSuccessfulChargeNotification(team, amount, newBalance, nextChargeDate, lang = 'en') {
  const locale = getLocale(lang);
  const formattedAmount = `$${amount.toFixed(2)}`;
  const formattedBalance = `$${newBalance.toFixed(2)}`;
  const formattedNextCharge = formatDateForLogging(nextChargeDate);
  return {
    title: locale.payment_processed_title,
    message: locale.payment_processed_message(formattedAmount, formattedBalance, formattedNextCharge),
    teamId: team.id,
    data: {
      type: "billing_success",
      charge_amount: amount.toString(),
      new_balance: newBalance.toString(),
      next_charge_date: nextChargeDate.toISOString(),
      team_id: team.id
    }
  };
}

/**
 * Insufficient funds notification template
 * @param {Object} team - Team object
 * @param {number} amount - Required charge amount
 * @param {number} currentBalance - Current balance
 * @param {string} lang - Language code (en/ru/ua)
 * @returns {Object} Notification object
 */
export function getInsufficientFundsNotification(team, amount, currentBalance, lang = 'en') {
  const locale = getLocale(lang);
  const formattedAmount = `$${amount.toFixed(2)}`;
  const formattedBalance = `$${currentBalance.toFixed(2)}`;
  const shortfall = `$${(amount - currentBalance).toFixed(2)}`;
  return {
    title: locale.insufficient_funds_title,
    message: locale.insufficient_funds_message(formattedAmount, formattedBalance, shortfall),
    teamId: team.id,
    data: {
      type: "billing_failure",
      required_amount: amount.toString(),
      current_balance: currentBalance.toString(),
      shortfall: (amount - currentBalance).toString(),
      team_id: team.id
    }
  };
}

/**
 * Service restored notification template
 * @param {Object} team - Team object
 * @param {number} newBalance - New balance after restoration
 * @param {string} lang - Language code (en/ru/ua)
 * @returns {Object} Notification object
 */
export function getServiceRestoredNotification(team, newBalance, lang = 'en') {
  const locale = getLocale(lang);
  const formattedBalance = `$${newBalance.toFixed(2)}`;
  return {
    title: locale.service_restored_title,
    message: locale.service_restored_message(formattedBalance),
    teamId: team.id,
    data: {
      type: "service_restored",
      new_balance: newBalance.toString(),
      team_id: team.id
    }
  };
}

/**
 * Billing error notification template (for admin/system alerts)
 * @param {Object} team - Team object
 * @param {string} error - Error message
 * @param {string} operation - Operation that failed
 * @param {string} lang - Language code (en/ru/ua)
 * @returns {Object} Notification object
 */
export function getBillingErrorNotification(team, error, operation, lang = 'en') {
  const locale = getLocale(lang);
  return {
    title: locale.billing_error_title,
    message: locale.billing_error_message(team.id, error, operation),
    teamId: team.id,
    data: {
      type: "billing_error",
      error_message: error,
      operation: operation,
      team_id: team.id,
      timestamp: new Date().toISOString()
    }
  };
}