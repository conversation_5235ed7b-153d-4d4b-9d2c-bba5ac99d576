Cron Plugin
This plugin adds support for running cronjob in the Elysia server.

Install with:


bun add @elysiajs/cron
Then use it:


import { Elysia } from 'elysia'
import { cron } from '@elysiajs/cron'

new Elysia()
	.use(
		cron({
			name: 'heartbeat',
			pattern: '*/10 * * * * *',
			run() {
				console.log('Heartbeat')
			}
		})
	)
	.listen(3000)
The above code will log heartbeat every 10 seconds.

cron
Create a cronjob for the Elysia server.

type:


cron(config: CronConfig, callback: (Instance['store']) => void): this
CronConfig accepts the parameters specified below:

name
Job name to register to store.

This will register the cron instance to store with a specified name, which can be used to reference in later processes eg. stop the job.

pattern
Time to run the job as specified by cron syntax specified as below:


┌────────────── second (optional)
│ ┌──────────── minute
│ │ ┌────────── hour
│ │ │ ┌──────── day of the month
│ │ │ │ ┌────── month
│ │ │ │ │ ┌──── day of week
│ │ │ │ │ │
* * * * * *
This can be generated by tools like Crontab Guru

This plugin extends the cron method to Elysia using cronner.

Below are the configs accepted by cronner.

timezone
Time zone in Europe/Stockholm format

startAt
Schedule start time for the job

stopAt
Schedule stop time for the job

maxRuns
Maximum number of executions

catch
Continue execution even if an unhandled error is thrown by a triggered function.

interval
The minimum interval between executions, in seconds.

Pattern
Below you can find the common patterns to use the plugin.

Stop cronjob
You can stop cronjob manually by accessing the cronjob name registered to store.


import { Elysia } from 'elysia'
import { cron } from '@elysiajs/cron'

const app = new Elysia()
	.use(
		cron({
			name: 'heartbeat',
			pattern: '*/1 * * * * *',
			run() {
				console.log('Heartbeat')
			}
		})
	)
	.get(
		'/stop',
		({
			store: {
				cron: { heartbeat }
			}
		}) => {
			heartbeat.stop()

			return 'Stop heartbeat'
		}
	)
	.listen(3000)
Predefined patterns
You can use predefined patterns from @elysiajs/cron/schedule


import { Elysia } from 'elysia'
import { cron, Patterns } from '@elysiajs/cron'

const app = new Elysia()
	.use(
		cron({
			name: 'heartbeat',
			pattern: Patterns.everySecond(),
			run() {
				console.log('Heartbeat')
			}
		})
	)
	.get(
		'/stop',
		({
			store: {
				cron: { heartbeat }
			}
		}) => {
			heartbeat.stop()

			return 'Stop heartbeat'
		}
	)
	.listen(3000)
Functions
Function	Description
.everySeconds(2)	Run the task every 2 seconds
.everyMinutes(5)	Run the task every 5 minutes
.everyHours(3)	Run the task every 3 hours
.everyHoursAt(3, 15)	Run the task every 3 hours at 15 minutes
.everyDayAt('04:19')	Run the task every day at 04:19
.everyWeekOn(Patterns.MONDAY, '19:30')	Run the task every Monday at 19:30
.everyWeekdayAt('17:00')	Run the task every day from Monday to Friday at 17:00
.everyWeekendAt('11:00')	Run the task on Saturday and Sunday at 11:00
Function aliases to constants
Function	Constant
.everySecond()	EVERY_SECOND
.everyMinute()	EVERY_MINUTE
.hourly()	EVERY_HOUR
.daily()	EVERY_DAY_AT_MIDNIGHT
.everyWeekday()	EVERY_WEEKDAY
.everyWeekend()	EVERY_WEEKEND
.weekly()	EVERY_WEEK
.monthly()	EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT
.everyQuarter()	EVERY_QUARTER
.yearly()	EVERY_YEAR
Constants
Constant	Pattern
.EVERY_SECOND	* * * * * *
.EVERY_5_SECONDS	*/5 * * * * *
.EVERY_10_SECONDS	*/10 * * * * *
.EVERY_30_SECONDS	*/30 * * * * *
.EVERY_MINUTE	*/1 * * * *
.EVERY_5_MINUTES	0 */5 * * * *
.EVERY_10_MINUTES	0 */10 * * * *
.EVERY_30_MINUTES	0 */30 * * * *
.EVERY_HOUR	0 0-23/1 * * *
.EVERY_2_HOURS	0 0-23/2 * * *
.EVERY_3_HOURS	0 0-23/3 * * *
.EVERY_4_HOURS	0 0-23/4 * * *
.EVERY_5_HOURS	0 0-23/5 * * *
.EVERY_6_HOURS	0 0-23/6 * * *
.EVERY_7_HOURS	0 0-23/7 * * *
.EVERY_8_HOURS	0 0-23/8 * * *
.EVERY_9_HOURS	0 0-23/9 * * *
.EVERY_10_HOURS	0 0-23/10 * * *
.EVERY_11_HOURS	0 0-23/11 * * *
.EVERY_12_HOURS	0 0-23/12 * * *
.EVERY_DAY_AT_1AM	0 01 * * *
.EVERY_DAY_AT_2AM	0 02 * * *
.EVERY_DAY_AT_3AM	0 03 * * *
.EVERY_DAY_AT_4AM	0 04 * * *
.EVERY_DAY_AT_5AM	0 05 * * *
.EVERY_DAY_AT_6AM	0 06 * * *
.EVERY_DAY_AT_7AM	0 07 * * *
.EVERY_DAY_AT_8AM	0 08 * * *
.EVERY_DAY_AT_9AM	0 09 * * *
.EVERY_DAY_AT_10AM	0 10 * * *
.EVERY_DAY_AT_11AM	0 11 * * *
.EVERY_DAY_AT_NOON	0 12 * * *
.EVERY_DAY_AT_1PM	0 13 * * *
.EVERY_DAY_AT_2PM	0 14 * * *
.EVERY_DAY_AT_3PM	0 15 * * *
.EVERY_DAY_AT_4PM	0 16 * * *
.EVERY_DAY_AT_5PM	0 17 * * *
.EVERY_DAY_AT_6PM	0 18 * * *
.EVERY_DAY_AT_7PM	0 19 * * *
.EVERY_DAY_AT_8PM	0 20 * * *
.EVERY_DAY_AT_9PM	0 21 * * *
.EVERY_DAY_AT_10PM	0 22 * * *
.EVERY_DAY_AT_11PM	0 23 * * *
.EVERY_DAY_AT_MIDNIGHT	0 0 * * *
.EVERY_WEEK	0 0 * * 0
.EVERY_WEEKDAY	0 0 * * 1-5
.EVERY_WEEKEND	0 0 * * 6,0
.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT	0 0 1 * *
.EVERY_1ST_DAY_OF_MONTH_AT_NOON	0 12 1 * *
.EVERY_2ND_HOUR	0 */2 * * *
.EVERY_2ND_HOUR_FROM_1AM_THROUGH_11PM	0 1-23/2 * * *
.EVERY_2ND_MONTH	0 0 1 */2 *
.EVERY_QUARTER	0 0 1 */3 *
.EVERY_6_MONTHS	0 0 1 */6 *
.EVERY_YEAR	0 0 1 1 *
.EVERY_30_MINUTES_BETWEEN_9AM_AND_5PM	0 */30 9-17 * * *
.EVERY_30_MINUTES_BETWEEN_9AM_AND_6PM	0 */30 9-18 * * *
.EVERY_30_MINUTES_BETWEEN_10AM_AND_7PM	0 */30 10-19 * * *